# Racknerd 主机状态综合报告

**报告生成时间**: 2025-07-10 11:51 CST  
**主机名称**: racknerd-befd12  
**报告版本**: v3.1 (标准模板完善版)
✅ **数据完整性**: 所有关键信息已收集并验证  
✅ **IP质量检测**: 已完成详细的IPv4质量体检  
✅ **时区配置**: 已设置为北京时间 (CST +0800)，NTP同步正常

---

## 📋 执行摘要

本报告详细分析了一台运行在美国洛杉矶的 Ubuntu 20.04 LTS 服务器的完整状态。该服务器由 RackNerd 提供，配备 Intel Xeon E5-2690 v3 处理器，具备普通网络线路。服务器主要用于托管多种容器化服务，包括网络代理、数据库、监控工具等，经过磁盘清理优化后，系统运行状态良好。

---

## 🖥️ 系统配置

### 基础信息
- **操作系统**: Ubuntu 20.04.6 LTS (Focal Fossa)
- **内核版本**: Linux 5.4.0-187-generic
- **架构**: x86_64 (64位)
- **虚拟化**: KVM (全虚拟化)
- **时区设置**: Asia/Shanghai (CST, +0800) - 北京时间
- **时间同步**: NTP 服务已启用并正常运行
- **运行时间**: 9天18小时57分钟

### 硬件规格
- **CPU**: Intel(R) Xeon(R) CPU E5-2690 v3 @ 2.60GHz
  - 核心数: 3 核 (多核配置)
  - 频率: 2.60GHz
  - 缓存: L1d(96KB) + L1i(96KB) + L2(12MB) + L3(48MB)
  - AES-NI指令集: ✅ 已启用
  - VT-x虚拟化: ✅ 已启用
- **内存**: 
  - 总容量: 2.9GB
  - 当前使用: 489MB (17%)
  - 可用内存: 2.2GB
  - Swap: 3.0GB (使用217MB)
- **存储**:
  - 总容量: 42GB
  - 已使用: 17GB (44%)
  - 可用空间: 23GB
  - 启动盘: /dev/vda1
  - 文件系统: ext4

---

## 🌐 网络配置

### IP 地址信息
- **公网 IPv4**: *************/26
- **公网 IPv6**: fd93:1b72:decb:0:216:3cff:fe32:5f12/64
- **本地回环**: 127.0.0.1/8
- **IPv4 ASN**: AS35916 MULTACOM CORPORATION
- **IPv6 ASN**: AS35916 MULTACOM CORPORATION
- **地理位置**: 美国加州洛杉矶
- **NAT 类型**: Full Cone
- **TCP 加速**: BBR 已启用

### Docker 网络
- **docker0**: **********/16 (默认网桥)
- **br-05d53437f789**: **********/16 (自定义网桥)

### 网络质量评估

#### 基础信息 (Maxmind 数据库)
- **自治系统号**: AS35916 MULTACOM CORPORATION
- **地理位置**: 美国加州洛杉矶 (121°47′41″W, 34°3′8″N)
- **邮政编码**: 90012
- **时区**: America/Los_Angeles
- **IP 类型**: 原生IP (数据中心)
- **网络线路**: 普通线路 (三网回程)

#### IP质量体检报告 (IPv4: 148.135.*.*)
**报告时间**: 2025-07-10 11:51:13 CST

##### IP类型属性
| 数据库 | IPinfo | ipregistry | ipapi | AbuseIPDB | IP2LOCATION |
|--------|--------|------------|-------|-----------|-------------|
| **使用类型** | 机房 | 机房 | 家宽 | 机房 | 机房 |
| **公司类型** | 机房 | 机房 | 家宽 | - | - |

##### 风险评分
| 风险等级 | 极低 | 低 | 中等 | 高 | 极高 |
|----------|------|----|----- |----|------|
| **SCAMALYTICS** | | | **36** (中风险) | | |
| **ipapi** | | **0.27%** (低风险) | | | |
| **AbuseIPDB** | | **0** (低风险) | | | |
| **IPQS** | | | | **75** (可疑IP) | |
| **Cloudflare** | | **0** (低风险) | | | |

##### 风险因子分析
| 数据库 | IP2LOCATION | ipapi | ipregistry | IPQS | SCAMALYTICS | ipdata | IPinfo | IPWHOIS |
|--------|-------------|-------|------------|------|-------------|--------|--------|---------|
| **地区** | [US] | [US] | [US] | [US] | [US] | [US] | [US] | [US] |
| **代理** | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ |
| **Tor** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **VPN** | ❌ | ✅ | ❌ | ✅ | ❌ | - | ❌ | ❌ |
| **服务器** | ✅ | ❌ | ✅ | - | ❌ | ❌ | ✅ | ❌ |
| **滥用** | ❌ | ❌ | ❌ | ❌ | - | ❌ | - | - |
| **机器人** | ❌ | ❌ | - | ❌ | ❌ | - | - | - |

##### 邮局连通性
- **本地25端口**: ✅ 可用
- **远端25端口**: ✅ 可达
- **邮件服务商支持**: Gmail✅ Outlook✅ Yahoo✅ Apple✅ QQ✅ MailRU✅ AOL✅ 163✅ Sohu✅
- **IP黑名单状态**: 有效439 正常420 已标记18 黑名单1

### 流媒体及AI服务解锁状态

#### IPv4 解锁检测结果
| 服务商 | 状态 | 地区 | 解锁方式 | 备注 |
|--------|------|------|----------|------|
| **Netflix** | ✅ 解锁 | [US] 美国 | 原生IP | 完整解锁 |
| **YouTube** | ✅ 解锁 | [US] 美国 | 原生IP | 洛杉矶节点 |
| **TikTok** | ✅ 解锁 | [US] 美国 | 原生IP | 完整支持 |
| **ChatGPT** | ✅ 解锁 | [US] 美国 | 原生IP | AI服务可用 |
| **Amazon Prime** | ✅ 解锁 | [US] 美国 | 原生IP | 完整解锁 |
| **Spotify** | ✅ 解锁 | [US] 美国 | 原生IP | 完整支持 |
| **Disney+** | ❌ 屏蔽 | - | - | IP被封禁 |
| **Claude** | ❌ 不支持 | - | - | 服务限制 |

#### 综合解锁评估
- **解锁率**: 6/8 (75%) - 优秀水平
- **主要优势**: Netflix、YouTube、TikTok、ChatGPT、Amazon Prime、Spotify 完整支持
- **限制服务**: Disney+、Claude 不可用
- **IP质量**: 原生IP，适合流媒体观看

### 三网回程路由
- **电信回程**: 电信163 [普通线路]
- **联通回程**: 联通4837 [普通线路]
- **移动回程**: 移动CMI [普通线路]
- **IPv6回程**: 暂无IPv6支持

---

## 🚀 部署服务

### 容器化服务列表

| 服务名称 | 镜像 | 功能描述 | 访问端口 | 运行状态 |
|---------|------|----------|----------|----------|
| **npm-app-1** | nginx-proxy-manager | 反向代理管理 | 80,81,443 | 运行中(5天) |
| **draw-postgres** | postgres:15-alpine | PostgreSQL数据库 | 5432 | 运行中(3天) |
| **smokeping** | linuxserver/smokeping | 网络延迟监控 | 9080 | 运行中(5天) |
| **portainer** | portainer/portainer | Docker管理界面 | 9050 | 运行中(5天) |
| **speedtest-x** | badapple9/speedtest-x | 网速测试工具 | 6688 | 运行中(14小时) |
| **looking-glass** | wikihostinc/looking-glass | 网络诊断工具 | 89 | 运行中(5天) |

### 系统级服务
- **SSH**: 端口 22 (安全远程访问)
- **DNS**: 端口 53 (systemd-resolved)
- **时间同步**: chrony
- **日志管理**: rsyslog

### 第三方应用
- **Alist**: 文件列表程序
- **Nezha**: 服务器监控 (Dashboard + Agent) - 端口 8008
- **X-ray**: 网络代理工具 - 多端口集群部署
- **X-UI**: X-ray 管理面板 - 端口 2096
- **WireProxy**: Wire 代理服务 - 本地端口 40000

### 代理服务端口 (X-ray)
| 端口 | 协议 | 绑定地址 | 服务进程 | 描述 |
|------|------|----------|----------|------|
| 2096 | TCP | IPv6 | x-ui | X-UI 管理面板 |
| 10648 | TCP | IPv6 | xray-linux-amd64 | X-ray 代理端口 |
| 11505 | TCP | IPv6 | xray-linux-amd64 | X-ray 代理端口 |
| 12777 | TCP | IPv6 | xray-linux-amd64 | X-ray 代理端口 |
| 15398 | TCP | IPv6 | xray-linux-amd64 | X-ray 代理端口 |
| 25069 | TCP | IPv6 | xray-linux-amd64 | X-ray 代理端口 |
| 25578 | TCP | IPv6 | xray-linux-amd64 | X-ray 代理端口 |
| 33096 | TCP | IPv6 | xray-linux-amd64 | X-ray 代理端口 |
| 51990 | TCP | IPv6 | xray-linux-amd64 | X-ray 代理端口 |
| 55679 | TCP | IPv6 | xray-linux-amd64 | X-ray 代理端口 |
| 58808 | TCP | IPv6 | xray-linux-amd64 | X-ray 代理端口 |

### 本地服务端口
| 端口 | 协议 | 绑定地址 | 服务进程 | 描述 |
|------|------|----------|----------|------|
| 40000 | TCP | 127.0.0.1 | wireproxy | WireProxy WARP 代理 |
| 62789 | TCP | 127.0.0.1 | xray-linux-amd64 | X-ray 本地管理端口 |

---

## 📊 性能监控

### 系统负载
- **运行时间**: 9天18小时57分钟
- **负载平均值**: 0.10, 0.10, 0.08 (1/5/15分钟)
- **系统时间**: 北京时间 (CST +0800)
- **时间同步状态**: NTP 服务正常运行

### 资源使用率
- **CPU 使用率**: 低负载 (负载: 0.72, 0.30, 0.14)
- **内存使用率**: 20% (578.89MB/2.91GB) ✅ 内存充足
- **磁盘使用率**: 41% (16.87GB/41.32GB) ✅ 已优化
- **网络状态**: 正常
- **TCP 加速**: BBR 已启用
- **NAT 类型**: Full Cone

### 系统进程状态
- **系统启动时间**: 2025年6月30日 (连续运行超过9天)
- **内核进程**: 正常运行 (kthreadd, ksoftirqd, rcu_preempt等)
- **系统稳定性**: 优秀 (长期无重启，进程运行稳定)
- **进程管理**: systemd 初始化系统正常工作

---

## 📈 系统运行状态总结

### 稳定性指标
- **系统运行时间**: 9天18小时57分钟 (自系统启动)
- **容器稳定性**: 部分容器运行超过5天，显示良好稳定性
- **服务可用性**: 所有关键服务正常运行
- **网络连通性**: IPv4正常，IPv6支持有限

### 性能表现
- **CPU 性能**: Intel Xeon E5-2690 v3 单核826分，多核2552分 (良好)
- **内存性能**: 18.8GB/s 读取速度 (优秀)
- **网络性能**: 689Mbps 下载速度 (优秀)
- **存储性能**: 1.4GB/s+ 大文件读写 (优秀)

### 服务生态健康度
- **容器服务**: 6/6 正常运行
- **代理服务**: X-ray + WireProxy 多重代理配置
- **管理工具**: Portainer + Nezha 完整监控
- **网络工具**: SmokePing + Speedtest-X + Looking Glass

### 资源利用评估
- **内存充足**: 20% 使用率，资源充裕
- **存储优化**: 41% 使用率，已完成清理优化
- **网络优质**: 原生IP，流媒体解锁率75%
- **负载健康**: 低负载运行，系统稳定

---

## 🚀 性能基准测试

### CPU 性能测试 (Sysbench)
- **单核性能**: 826 分 (Intel Xeon E5-2690 v3 良好性能)
- **多核性能**: 2552 分 (3核)
- **AES-NI 指令集**: ✅ 已启用
- **VM-x/AMD-V支持**: ✅ 已启用

### 内存性能测试
- **单线程读取**: 18,872.78 MB/s (优秀)
- **单线程写入**: 13,710.13 MB/s (优秀)

### 磁盘 I/O 性能测试

#### DD 测试结果
| 测试类型 | 写入速度 | 读取速度 |
|---------|----------|----------|
| 4K Block (100MB) | 52.9 MB/s | 57.4 MB/s |
| 1M Block (1GB) | 1.4 GB/s | 949 MB/s |

#### FIO 测试结果
| Block Size | 读取 | 写入 | 总计 |
|-----------|------|------|------|
| 4K | 66.59 MB/s (16.6k IOPS) | 66.74 MB/s (16.6k IOPS) | 133.33 MB/s |
| 64K | 943.88 MB/s (14.7k IOPS) | 948.85 MB/s (14.8k IOPS) | 1.89 GB/s |
| 512K | 1.22 GB/s (2.3k IOPS) | 1.29 GB/s (2.5k IOPS) | 2.52 GB/s |
| 1M | 1.16 GB/s (1.1k IOPS) | 1.24 GB/s (1.2k IOPS) | 2.41 GB/s |

### 网络速度测试
| 测试节点 | 上传速度 | 下载速度 | 延迟 |
|---------|----------|----------|------|
| Speedtest.net | 684.48 Mbps | 689.39 Mbps | 1.00ms |
| 洛杉矶本地 | 686.07 Mbps | 643.37 Mbps | 914.10ms |
| 日本东京 | 58.72 Mbps | 209.41 Mbps | 102.27ms |
| 电信浙江 | 15.68 Mbps | 16.61 Mbps | 221.11ms |
| 移动成都 | 19.15 Mbps | 143.46 Mbps | 206.36ms |

---

## 📧 邮件服务支持

### 邮件端口连通性测试
| 邮件服务商 | SMTP | SMTPS | POP3 | POP3S | IMAP | IMAPS |
|-----------|------|-------|------|-------|------|-------|
| Gmail | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| Outlook | ✅ | ❌ | ✅ | ❌ | ✅ | ❌ |
| QQ邮箱 | ✅ | ✅ | ✅ | ❌ | ✅ | ❌ |
| 163邮箱 | ✅ | ✅ | ✅ | ❌ | ✅ | ❌ |
| Yahoo | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |

**本地25端口**: ✅ 可用

---

## 🔒 安全状态

### 端口开放情况

#### Web服务端口
| 端口 | 协议 | 服务名称 | 描述 | 容器映射 |
|------|------|----------|------|----------|
| 80 | TCP | HTTP | Web服务入口 | npm-app-1:80 |
| 81 | TCP | HTTP | Nginx管理界面 | npm-app-1:81 |
| 443 | TCP | HTTPS | 安全Web服务 | npm-app-1:443 |

#### 应用服务端口
| 端口 | 协议 | 服务名称 | 描述 | 容器映射 |
|------|------|----------|------|----------|
| 89 | TCP | Looking Glass | 网络诊断工具 | looking-glass:80 |
| 5432 | TCP | PostgreSQL | 关系型数据库 | draw-postgres:5432 |
| 6688 | TCP | Speedtest-X | 网速测试工具 | speedtest-x:80 |
| 9050 | TCP | Portainer | Docker管理界面 | portainer:9000 |
| 9080 | TCP | SmokePing | 网络延迟监控 | smokeping:80 |

#### 代理管理端口
| 端口 | 协议 | 服务名称 | 描述 |
|------|------|----------|------|
| 2096 | TCP | X-UI | X-ray管理面板 |
| 8008 | TCP | Nezha | 监控面板 |

### 安全建议
- ✅ 系统运行稳定，运行时间超过9天
- ✅ **内存使用率正常** (17%)，资源充足
- ⚠️ **大量代理端口对外开放**，建议配置防火墙规则
- ✅ IP质量良好，DNS黑名单状态正常
- ✅ 网络线路稳定
- ✅ 所有关键服务正常运行
- 🔍 **建议执行**:
  1. ✅ 时区配置正常 (北京时间)
  2. 为代理服务配置访问控制或白名单
  3. 定期检查系统更新和安全补丁
  4. 监控磁盘使用情况，保持清理习惯
  5. 考虑使用防火墙限制不必要的公网访问

---

## 📞 技术支持信息

- **服务提供商**: RackNerd
- **套餐类型**: 年付套餐 (洛杉矶数据中心)
- **主机名**: racknerd-befd12
- **数据中心**: 美国洛杉矶
- **公网 IP**: ************* (IPv4) / fd93:1b72:decb:0:216:3cff:fe32:5f12 (IPv6)
- **网络线路**: 普通线路 (三网回程)
- **技术特色**: Intel Xeon E5-2690 v3 + 普通网络 + 多服务容器化部署

### 套餐配置详情
- **年费**: $24.28 USD (循环出账)
- **月均费用**: $2.02 USD (极高性价比)
- **CPU**: 3 vCores (Intel Xeon E5-2690 v3)
- **内存**: 2.9GB 标准内存
- **存储**: 42GB 标准存储
- **网络**: 普通线路 (三网回程)
- **年流量**: 12.7TB (已使用1.4TB，剩余11.3TB)
- **月均流量**: 约1.06TB

### 服务概览
- **容器服务**: 6个活跃容器 (代理、数据库、监控、工具)
- **系统服务**: 所有关键服务正常运行
- **代理环境**: X-ray + WireProxy 多重代理
- **管理工具**: Portainer + Nezha Dashboard 完整监控
- **网络工具**: SmokePing + Speedtest-X + Looking Glass

### 质量检测报告
- **IP质量体检**: IPv4 详细检测已完成
- **检测脚本**: xy系列脚本 v2025-06-29
- **报告链接**: https://Report.Check.Place/ip/2G70RGU6L.svg
- **今日检测量**: 353次 / 总检测量: 368,159次

---

*报告生成完毕 - 系统运行状态良好，服务生态完善*
