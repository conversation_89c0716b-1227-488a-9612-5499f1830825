# 甲骨文ARM 主机状态综合报告

**报告生成时间**: 2025-07-10 11:50 CST  
**主机名称**: arm-20241203-1030  
**报告版本**: v3.0 (标准模板版)  
✅ **数据完整性**: 所有关键信息已收集并验证  
✅ **IP质量检测**: 已完成详细的IPv6质量体检  
✅ **时区配置**: UTC 标准时间，NTP 同步正常

---

## 📋 执行摘要

本报告详细分析了一台运行在美国加州圣何塞的 Ubuntu 24.04 LTS ARM 服务器的完整状态。该服务器由 Oracle Cloud 提供，配备 ARM Neoverse-N1 处理器，具备 24GB 大内存配置。服务器部署了完整的 Web 开发环境，包括 WordPress、数据库、容器管理等服务，系统运行极其稳定，已连续运行超过 182 天。

---

## 🖥️ 系统配置

### 基础信息
- **操作系统**: Ubuntu 24.04.1 LTS (Noble Numbat)
- **内核版本**: Linux 6.8.0-1018-oracle
- **架构**: aarch64 (ARM 64位)
- **虚拟化**: KVM (全虚拟化)
- **时区设置**: Etc/UTC (UTC, +0000) - 标准协调时间
- **时间同步**: NTP 服务已启用并正常运行
- **运行时间**: 182天19小时43分钟 (极高稳定性)

### 硬件规格
- **CPU**: ARM Neoverse-N1 BIOS virt-7.2 @ 2.0GHz
  - 核心数: 4 核 (ARM 高性能架构)
  - 频率: 2.0GHz
  - 缓存: 虚拟化环境 (缓存信息不可见)
  - AES-NI指令集: ✅ 已启用
  - ARM虚拟化: ❌ 已禁用
- **内存**: 
  - 总容量: 23.42GB (大内存配置)
  - 当前使用: 2.27GB (10%)
  - 可用内存: 21.15GB
  - Swap: 未配置
- **存储**:
  - 总容量: 91.95GB
  - 已使用: 16.97GB (18%)
  - 可用空间: 74.98GB
  - 启动盘: /dev/sda1
  - 文件系统: ext4

---

## 🌐 网络配置

### IP 地址信息
- **内网 IPv4**: **********/24
- **公网 IPv6**: 2603:c024:c014:48e:d557:f83c:b158:c234/128
- **本地回环**: 127.0.0.1/8
- **IPv4 ASN**: AS31898 Oracle Corporation
- **IPv6 ASN**: AS31898 Oracle Cloud
- **地理位置**: 美国加州圣何塞
- **NAT 类型**: Full Cone
- **TCP 加速**: BBR 已启用

### Docker 网络
- **docker0**: **********/16 (默认网桥)
- **br-91aeeb1a4a74**: **********/16 (活跃)
- **br-58f2f75d7121**: **********/16 (未使用)

### 网络质量评估

#### 基础信息 (Maxmind 数据库)
- **自治系统号**: AS31898 ORACLE-BMC-31898
- **地理位置**: 美国加州圣何塞 (121°47′41″W, 37°14′16″N)
- **邮政编码**: 95119
- **时区**: America/Los_Angeles
- **IP 类型**: 原生IP (数据中心)
- **网络线路**: 普通线路 (三网回程)

#### IP质量体检报告 (IPv6: 2603:c024:c014:*)
**报告时间**: 2025-07-10 11:50:45 CST

##### IP类型属性
| 数据库 | IPinfo | ipregistry | ipapi | AbuseIPDB | IP2LOCATION |
|--------|--------|------------|-------|-----------|-------------|
| **使用类型** | 机房 | 机房 | 商业 | 机房 | 机房 |
| **公司类型** | 机房 | 机房 | 商业 | - | - |

##### 风险评分
| 风险等级 | 极低 | 低 | 中等 | 高 | 极高 |
|----------|------|----|----- |----|------|
| **SCAMALYTICS** | | | **25** (中风险) | | |
| **ipapi** | **0.00%** (极低风险) | | | | |
| **AbuseIPDB** | | **0** (低风险) | | | |
| **IPQS** | | | | **75** (可疑IP) | |
| **Cloudflare** | | **0** (低风险) | | | |

##### 风险因子分析
| 数据库 | IP2LOCATION | ipapi | ipregistry | IPQS | SCAMALYTICS | ipdata | IPinfo | IPWHOIS |
|--------|-------------|-------|------------|------|-------------|--------|--------|---------|
| **地区** | [US] | [US] | [US] | [US] | [US] | [US] | [US] | [US] |
| **代理** | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ |
| **Tor** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **VPN** | ❌ | ❌ | ❌ | ✅ | ❌ | - | ❌ | ❌ |
| **服务器** | ✅ | ❌ | ✅ | - | ❌ | ❌ | ✅ | ❌ |
| **滥用** | ❌ | ❌ | ❌ | ❌ | - | ❌ | - | - |
| **机器人** | ❌ | ❌ | - | ❌ | ❌ | - | - | - |

##### 邮局连通性
- **本地25端口**: ❌ 阻断
- **远端25端口**: ❌ 不可达

### 流媒体及AI服务解锁状态

#### IPv6 解锁检测结果
| 服务商 | 状态 | 地区 | 解锁方式 | 备注 |
|--------|------|------|----------|------|
| **Netflix** | ✅ 解锁 | [US] 美国 | 原生IP | 完整解锁 |
| **YouTube** | ✅ 解锁 | [US] 美国 | 原生IP | 支持高清 |
| **Disney+** | ✅ 解锁 | [US] 美国 | 原生IP | 完整解锁 |
| **ChatGPT** | ✅ 解锁 | [US] 美国 | 原生IP | AI服务可用 |
| **TikTok** | ❌ 失败 | - | - | IPv6连接失败 |
| **Amazon Prime** | ❌ 屏蔽 | - | - | IP被屏蔽 |
| **Spotify** | ❌ 屏蔽 | - | - | 注册受限 |

#### 综合解锁评估
- **解锁率**: 4/7 (57%) - 中等水平
- **主要优势**: Netflix、YouTube、Disney+、ChatGPT 完整支持
- **限制服务**: TikTok、Amazon Prime、Spotify 不可用
- **IP质量**: 原生IP，适合流媒体观看

### 三网回程路由
- **电信回程**: 电信163 [普通线路]
- **联通回程**: 联通4837 [普通线路]
- **移动回程**: 移动CMI [普通线路]
- **IPv6回程**: 移动CMIN2 [精品线路] + 移动CMI [普通线路]

---

## 🚀 部署服务

### 容器化服务列表

| 服务名称 | 镜像 | 功能描述 | 访问端口 | 运行状态 |
|---------|------|----------|----------|----------|
| **portainer** | portainer-ce | Docker管理界面 | 8001,9000 | 运行中(7个月) |
| **smokeping** | linuxserver/smokeping | 网络延迟监控 | 9080 | 运行中(7个月) |
| **speedtest-x** | badapple9/speedtest-x | 网速测试工具 | 6688 | 运行中(7个月) |
| **wordpress** | wordpress:6.7.1 | WordPress博客 | 8080(本地) | 运行中(6个月) |
| **mariadb** | mariadb:11.6.2 | MySQL数据库 | 3306(本地) | 运行中(6个月) |

### 系统级服务
- **SSH**: 端口 22 (安全远程访问)
- **1Panel**: 端口 11300 (服务器管理面板)
- **X-UI**: 端口 2096 (X-ray 管理面板)
- **Nezha Agent**: 服务器监控代理
- **PHP-FPM**: PHP 8.0 运行环境

### 代理服务端口 (X-ray)
| 端口 | 协议 | 绑定地址 | 服务进程 | 描述 |
|------|------|----------|----------|------|
| 2096 | TCP | IPv6 | x-ui | X-UI 管理面板 |
| 16027 | TCP | IPv6 | xray-linux-arm64 | X-ray 代理端口 |
| 16168 | TCP | IPv6 | xray-linux-arm64 | X-ray 代理端口 |
| 32388 | TCP | IPv6 | xray-linux-arm64 | X-ray 代理端口 |
| 38888 | TCP | IPv6 | xray-linux-arm64 | X-ray 代理端口 |

### 本地服务端口
| 端口 | 协议 | 绑定地址 | 服务进程 | 描述 |
|------|------|----------|----------|------|
| 62789 | TCP | 127.0.0.1 | xray-linux-arm64 | X-ray 本地管理端口 |
| 8461 | TCP | 127.0.0.1 | mta-sts-daemon | 邮件安全服务 |

---

## 📊 性能监控

### 系统负载
- **运行时间**: 182天19小时43分钟
- **负载平均值**: 0.84, 0.22, 0.07 (1/5/15分钟)
- **系统时间**: UTC 标准时间 (Etc/UTC +0000)
- **时间同步状态**: NTP 服务正常运行

### 资源使用率
- **CPU 使用率**: 低负载 (负载: 0.84, 0.22, 0.07)
- **内存使用率**: 10% (2.27GB/23.42GB) ✅ 内存充足
- **磁盘使用率**: 18% (16.97GB/91.95GB) ✅ 存储充足
- **网络状态**: 正常
- **TCP 加速**: BBR 已启用
- **NAT 类型**: Full Cone

### 系统进程状态
- **系统启动时间**: 2025年1月8日 (连续运行超过182天)
- **内核进程**: 正常运行 (kthreadd, ksoftirqd, rcu_sched等)
- **系统稳定性**: 优秀 (长期无重启，进程运行稳定)
- **进程管理**: systemd 初始化系统正常工作

---

## 📈 系统运行状态总结

### 稳定性指标
- **系统运行时间**: 182天19小时 (自2025年1月8日启动)
- **容器稳定性**: 部分容器运行超过7个月，显示极高稳定性
- **服务可用性**: 30个系统服务全部正常运行
- **网络连通性**: IPv6正常，IPv4仅内网

### 性能表现
- **CPU 性能**: ARM Neoverse-N1 单核3358分，多核13336分 (优秀)
- **内存性能**: 30GB/s 读取速度 (优秀)
- **网络性能**: 3.8Gbps 下载速度 (优秀)
- **存储性能**: 55MB/s 大文件读写 (中等)

### 服务生态健康度
- **容器服务**: 5/5 正常运行
- **Web开发环境**: WordPress + MariaDB 完整配置
- **管理工具**: 1Panel + Portainer 双重管理
- **代理服务**: X-ray 多端口配置

---

## 🚀 性能基准测试

### CPU 性能测试 (Sysbench)
- **单核性能**: 3358 分 (ARM Neoverse-N1 优秀性能)
- **多核性能**: 13336 分 (4核)
- **AES-NI 指令集**: ✅ 已启用
- **ARM 虚拟化**: ❌ 已禁用

### 内存性能测试
- **单线程读取**: 30,794.32 MB/s (优秀)
- **单线程写入**: 14,581.74 MB/s (优秀)

### 磁盘 I/O 性能测试

#### DD 测试结果
| 测试类型 | 写入速度 | 读取速度 |
|---------|----------|----------|
| 4K Block (100MB) | 7.7 MB/s | 9.7 MB/s |
| 1M Block (1GB) | 55.2 MB/s | 52.3 MB/s |

#### FIO 测试结果
| Block Size | 读取 | 写入 | 总计 |
|-----------|------|------|------|
| 4K | 12.21 MB/s (3.0k IOPS) | 12.21 MB/s (3.0k IOPS) | 24.42 MB/s |
| 64K | 26.80 MB/s (418 IOPS) | 27.59 MB/s (431 IOPS) | 54.39 MB/s |
| 512K | 24.18 MB/s (47 IOPS) | 26.24 MB/s (51 IOPS) | 50.42 MB/s |
| 1M | 24.03 MB/s (23 IOPS) | 26.81 MB/s (26 IOPS) | 50.85 MB/s |

### 网络速度测试
| 测试节点 | 上传速度 | 下载速度 | 延迟 |
|---------|----------|----------|------|
| Speedtest.net | 3922.76 Mbps | 3802.38 Mbps | 860.54ms |
| 洛杉矶本地 | 1522.99 Mbps | 1244.68 Mbps | 8.53ms |
| 日本东京 | 132.55 Mbps | 260.54 Mbps | 120.64ms |
| 电信浙江 | 113.80 Mbps | 190.66 Mbps | 140.19ms |
| 移动成都 | 27.35 Mbps | 127.92 Mbps | 429.27ms |

---

## 📧 邮件服务支持

### 邮件端口连通性测试
| 邮件服务商 | SMTP | SMTPS | POP3 | POP3S | IMAP | IMAPS |
|-----------|------|-------|------|-------|------|-------|
| Gmail | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Outlook | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| QQ邮箱 | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 163邮箱 | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Yahoo | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |

**本地25端口**: ❌ 阻断

---

## 🔒 安全状态

### 端口开放情况

#### Web服务端口
| 端口 | 协议 | 服务名称 | 描述 | 容器映射 |
|------|------|----------|------|----------|
| 6688 | TCP | Speedtest-X | 网速测试工具 | speedtest-x:80 |
| 8001 | TCP | Portainer | Docker管理界面 | portainer:8000 |
| 9000 | TCP | Portainer HTTPS | Docker管理界面 | portainer:9443 |
| 9080 | TCP | SmokePing | 网络延迟监控 | smokeping:80 |
| 11300 | TCP | 1Panel | 服务器管理面板 | 系统服务 |

#### 应用服务端口
| 端口 | 协议 | 绑定地址 | 服务名称 | 描述 |
|------|------|----------|----------|------|
| 3306 | TCP | 127.0.0.1 | MariaDB | MySQL数据库 |
| 8080 | TCP | 127.0.0.1 | WordPress | WordPress博客 |

#### 代理管理端口
| 端口 | 协议 | 服务名称 | 描述 |
|------|------|----------|------|
| 2096 | TCP | X-UI | X-ray管理面板 |

### 安全建议
- ✅ 系统运行极其稳定，运行时间超过182天
- ✅ **内存使用率极低** (10%)，资源充足
- ✅ **磁盘使用率低** (18%)，存储充足
- ❌ **邮件端口被阻断**，无法发送邮件
- ✅ ARM 架构安全性较高
- ✅ 本地服务合理绑定，安全性良好
- 🔍 **建议执行**:
  1. ✅ 时区配置正常 (UTC 标准时间)
  2. 考虑配置 Swap 分区以提高系统稳定性
  3. 定期备份 WordPress 和数据库数据
  4. 监控代理服务的访问情况
  5. 如需要可调整为本地时区: `sudo timedatectl set-timezone Asia/Shanghai`

---

## 📞 技术支持信息

- **服务提供商**: Oracle Cloud Infrastructure
- **主机名**: arm-20241203-1030
- **数据中心**: 美国加州圣何塞
- **内网 IP**: ********** (IPv4) / 2603:c024:c014:48e:d557:f83c:b158:c234 (IPv6)
- **网络线路**: 普通线路 (三网回程)
- **技术特色**: ARM Neoverse-N1 + 24GB大内存 + 完整Web开发环境

### 免费资源配额
- **OCPU时间**: 3000小时/月 (4C配置可运行750小时，约25天)
- **内存使用**: 18000GB·小时/月 (24GB配置可运行750小时)
- **网络流量**: 10TB/月 (出入流量合计)
- **存储空间**: 200GB总配额 (启动盘+数据盘)
- **适用范围**: 试用用户和付费用户均享受相同免费额度
- **成本优势**: 完全免费，无隐藏费用

### 服务概览
- **容器服务**: 5个活跃容器 (Web开发、监控、管理)
- **系统服务**: 30个系统服务正常运行
- **Web环境**: WordPress + MariaDB + PHP-FPM
- **管理工具**: 1Panel + Portainer 双重管理
- **代理服务**: X-ray 多端口配置

### 质量检测报告
- **IP质量体检**: IPv6 详细检测已完成
- **检测脚本**: xy系列脚本 v2025-06-29
- **报告链接**: https://Report.Check.Place/ip/2Z9FD9Y8J.svg
- **今日检测量**: 352次 / 总检测量: 368,158次

---

*报告生成完毕 - 系统运行状态优秀，ARM架构性能表现出色*
