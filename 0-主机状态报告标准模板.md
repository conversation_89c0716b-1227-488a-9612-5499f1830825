# [服务商名称] 主机状态综合报告

**报告生成时间**: YYYY-MM-DD HH:MM 时区  
**主机名称**: [实际主机名]  
**报告版本**: vX.X ([版本说明])  
✅ **数据完整性**: [数据收集状态说明]  
✅ **时区配置**: [时区设置状态]

---

## 📋 执行摘要

本报告详细分析了一台运行在[地理位置]的[操作系统]服务器的完整状态。该服务器由[服务提供商]提供，配备[主要硬件特色]，具备[网络线路特色]。[服务器主要用途描述]，系统运行[稳定性评估]。

---

## 🖥️ 系统配置

### 基础信息
- **操作系统**: [OS版本] ([代号])
- **内核版本**: Linux [内核版本]
- **架构**: [x86_64/aarch64] ([位数]位)
- **虚拟化**: [KVM/其他] ([虚拟化类型])
- **时区设置**: [时区] ([UTC偏移]) - [时区描述]
- **时间同步**: [NTP状态]
- **运行时间**: [运行天数]

### 硬件规格
- **CPU**: [CPU型号]
  - 核心数: [X] 核 ([特殊说明])
  - 频率: [频率]
  - 缓存: L1([大小]) + L2([大小]) + L3([大小])
  - [特殊指令集]: ✅/❌ [状态]
  - [虚拟化支持]: ✅/❌ [状态]
- **内存**: 
  - 总容量: [容量]
  - 当前使用: [使用量] ([百分比]%)
  - 可用内存: [可用量]
  - Swap: [Swap配置]
- **存储**:
  - 总容量: [容量]
  - 已使用: [使用量] ([百分比]%)
  - 可用空间: [可用量]
  - 启动盘: [设备路径]

---

## 🌐 网络配置

### IP 地址信息
- **公网 IPv4**: [IPv4地址/子网]
- **公网 IPv6**: [IPv6地址/子网]
- **内网 IP**: [内网地址] (如适用)
- **本地回环**: 127.0.0.1/8
- **IPv4 ASN**: [ASN信息]
- **IPv6 ASN**: [ASN信息]
- **地理位置**: [详细位置]
- **NAT 类型**: [NAT类型]
- **TCP 加速**: [加速方式] 已启用

### Docker 网络
- **docker0**: [网段] (默认网桥)
- **[自定义网桥1]**: [网段] ([状态])
- **[自定义网桥2]**: [网段] ([状态])

### 网络质量评估

#### 基础信息 (Maxmind 数据库)
- **自治系统号**: [ASN详细信息]
- **地理位置**: [详细地理信息]
- **邮政编码**: [邮编]
- **时区**: [网络时区]
- **IP 类型**: [IP类型] ([用途])
- **网络线路**: [线路类型] ([线路描述])

#### IP质量体检报告 ([IP类型]: [IP地址段])
**报告时间**: [检测时间]

##### IP类型属性
| 数据库 | IPinfo | ipregistry | ipapi | AbuseIPDB | IP2LOCATION |
|--------|--------|------------|-------|-----------|-------------|
| **使用类型** | [类型] | [类型] | [类型] | [类型] | [类型] |
| **公司类型** | [类型] | [类型] | [类型] | - | - |

##### 风险评分
| 风险等级 | 极低 | 低 | 中等 | 高 | 极高 |
|----------|------|----|----- |----|------|
| **SCAMALYTICS** | | | **[分数]** ([风险级别]) | | |
| **ipapi** | **[分数]%** ([风险级别]) | | | | |
| **AbuseIPDB** | | **[分数]** ([风险级别]) | | | |
| **IPQS** | | | | **[分数]** ([风险级别]) | |
| **Cloudflare** | | **[分数]** ([风险级别]) | | | |

##### 风险因子分析
| 数据库 | IP2LOCATION | ipapi | ipregistry | IPQS | SCAMALYTICS | ipdata | IPinfo | IPWHOIS |
|--------|-------------|-------|------------|------|-------------|--------|--------|---------|
| **地区** | [地区] | [地区] | [地区] | [地区] | [地区] | [地区] | [地区] | [地区] |
| **代理** | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ |
| **Tor** | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ |
| **VPN** | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | - | ✅/❌ | ✅/❌ |
| **服务器** | ✅/❌ | ✅/❌ | ✅/❌ | - | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ |
| **滥用** | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | - | ✅/❌ | - | - |
| **机器人** | ✅/❌ | ✅/❌ | - | ✅/❌ | ✅/❌ | - | - | - |

##### 邮局连通性
- **本地25端口**: ✅/❌ [状态]
- **远端25端口**: ✅/❌ [状态]

### 流媒体及AI服务解锁状态

#### [IP类型] 解锁检测结果
| 服务商 | 状态 | 地区 | 解锁方式 | 备注 |
|--------|------|------|----------|------|
| **Netflix** | ✅/❌ [状态] | [地区] | [方式] | [备注] |
| **YouTube** | ✅/❌ [状态] | [地区] | [方式] | [备注] |
| **Disney+** | ✅/❌ [状态] | [地区] | [方式] | [备注] |
| **ChatGPT** | ✅/❌ [状态] | [地区] | [方式] | [备注] |
| **TikTok** | ✅/❌ [状态] | [地区] | [方式] | [备注] |
| **Amazon Prime** | ✅/❌ [状态] | [地区] | [方式] | [备注] |
| **Spotify** | ✅/❌ [状态] | [地区] | [方式] | [备注] |

#### 综合解锁评估
- **解锁率**: [X]/[总数] ([百分比]%) - [评级]
- **主要优势**: [支持的主要服务]
- **限制服务**: [不支持的服务]
- **IP质量**: [IP质量评估]

### 三网回程路由
- **电信回程**: [线路类型] [线路等级]
- **联通回程**: [线路类型] [线路等级]
- **移动回程**: [线路类型] [线路等级]
- **IPv6回程**: [线路类型] [线路等级]

---

## 🚀 部署服务

### 容器化服务列表

| 服务名称 | 镜像 | 功能描述 | 访问端口 | 运行状态 |
|---------|------|----------|----------|----------|
| **[服务1]** | [镜像名] | [功能描述] | [端口] | 运行中([时长]) |
| **[服务2]** | [镜像名] | [功能描述] | [端口] | 运行中([时长]) |

### 系统级服务
- **SSH**: 端口 22 ([描述])
- **[服务名]**: 端口 [端口] ([描述])

### 代理服务端口 ([代理类型])
| 端口 | 协议 | 绑定地址 | 服务进程 | 描述 |
|------|------|----------|----------|------|
| [端口] | TCP | [地址] | [进程名] | [描述] |

### 本地服务端口
| 端口 | 协议 | 绑定地址 | 服务进程 | 描述 |
|------|------|----------|----------|------|
| [端口] | TCP | 127.0.0.1 | [进程名] | [描述] |

---

## 📊 性能监控

### 系统负载
- **运行时间**: [详细运行时间]
- **负载平均值**: [1分钟], [5分钟], [15分钟] (1/5/15分钟)
- **系统时间**: [时区描述]
- **时间同步状态**: [NTP状态]

### 资源使用率
- **CPU 使用率**: [负载描述] (负载: [具体数值])
- **内存使用率**: [百分比]% ([使用量]/[总量]) [状态评估]
- **磁盘使用率**: [百分比]% ([使用量]/[总量]) [状态评估]
- **网络状态**: [状态]
- **TCP 加速**: [加速方式] 已启用
- **NAT 类型**: [NAT类型]

### 系统进程状态
- **系统启动时间**: [启动日期] ([连续运行描述])
- **内核进程**: [状态描述]
- **系统稳定性**: [稳定性评估]
- **进程管理**: [进程管理系统] 正常工作

---

## 📈 系统运行状态总结

### 稳定性指标
- **系统运行时间**: [详细时间] ([稳定性描述])
- **容器稳定性**: [容器稳定性描述]
- **服务可用性**: [服务数量]个系统服务全部正常运行
- **网络连通性**: [网络状态描述]

### 性能表现
- **CPU 性能**: [CPU型号] [性能评分] ([性能等级])
- **内存性能**: [速度] ([性能等级])
- **网络性能**: [速度] ([性能等级])
- **存储性能**: [速度] ([性能等级])

### 服务生态健康度
- **容器服务**: [运行数量]/[总数] 正常运行
- **[特色服务]**: [服务描述]
- **管理工具**: [管理工具列表]
- **[其他服务]**: [服务描述]

---

## 🚀 性能基准测试

### CPU 性能测试 (Sysbench)
- **单核性能**: [分数] 分 ([CPU描述])
- **多核性能**: [分数] 分 ([核心数]核)
- **[特殊指令集]**: ✅/❌ 已启用/已禁用
- **[虚拟化支持]**: ✅/❌ 已启用/已禁用

### 内存性能测试
- **单线程读取**: [速度] MB/s ([性能等级])
- **单线程写入**: [速度] MB/s ([性能等级])

### 磁盘 I/O 性能测试

#### DD 测试结果
| 测试类型 | 写入速度 | 读取速度 |
|---------|----------|----------|
| 4K Block (100MB) | [速度] MB/s | [速度] MB/s |
| 1M Block (1GB) | [速度] MB/s | [速度] MB/s |

#### FIO 测试结果
| Block Size | 读取 | 写入 | 总计 |
|-----------|------|------|------|
| 4K | [速度] MB/s ([IOPS]k IOPS) | [速度] MB/s ([IOPS]k IOPS) | [速度] MB/s |
| 64K | [速度] MB/s ([IOPS] IOPS) | [速度] MB/s ([IOPS] IOPS) | [速度] MB/s |
| 512K | [速度] MB/s ([IOPS] IOPS) | [速度] MB/s ([IOPS] IOPS) | [速度] MB/s |
| 1M | [速度] MB/s ([IOPS] IOPS) | [速度] MB/s ([IOPS] IOPS) | [速度] MB/s |

### 网络速度测试
| 测试节点 | 上传速度 | 下载速度 | 延迟 |
|---------|----------|----------|------|
| Speedtest.net | [速度] Mbps | [速度] Mbps | [延迟]ms |
| [本地节点] | [速度] Mbps | [速度] Mbps | [延迟]ms |
| [国际节点] | [速度] Mbps | [速度] Mbps | [延迟]ms |

---

## 📧 邮件服务支持

### 邮件端口连通性测试
| 邮件服务商 | SMTP | SMTPS | POP3 | POP3S | IMAP | IMAPS |
|-----------|------|-------|------|-------|------|-------|
| Gmail | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ |
| Outlook | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ |
| QQ邮箱 | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ |
| 163邮箱 | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ |
| Yahoo | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ |

**本地25端口**: ✅/❌ [状态]

---

## 🔒 安全状态

### 端口开放情况

#### Web服务端口
| 端口 | 协议 | 服务名称 | 描述 | 容器映射 |
|------|------|----------|------|----------|
| [端口] | TCP | [服务名] | [描述] | [映射] |

#### 应用服务端口
| 端口 | 协议 | 服务名称 | 描述 | 容器映射 |
|------|------|----------|------|----------|
| [端口] | TCP | [服务名] | [描述] | [映射] |

#### 代理管理端口
| 端口 | 协议 | 服务名称 | 描述 |
|------|------|----------|------|
| [端口] | TCP | [服务名] | [描述] |

### 安全建议
- ✅ [安全状态描述]
- ⚠️ **[风险描述]**: [具体说明]
- ✅ [其他安全状态]
- 🔍 **建议执行**:
  1. [具体建议1]
  2. [具体建议2]
  3. [具体建议3]

---

## 📞 技术支持信息

- **服务提供商**: [提供商名称]
- **主机名**: [主机名]
- **数据中心**: [数据中心位置]
- **公网 IP**: [IPv4] (IPv4) / [IPv6] (IPv6)
- **网络线路**: [线路描述]
- **技术特色**: [主要技术特色]

### 服务概览
- **容器服务**: [数量]个活跃容器 ([服务类型])
- **系统服务**: [数量]个系统服务正常运行
- **[特色环境]**: [环境描述]
- **管理工具**: [管理工具列表]
- **[其他服务]**: [服务描述]

### 质量检测报告 (如适用)
- **IP质量体检**: [检测类型] 详细检测已完成
- **检测脚本**: [脚本信息]
- **报告链接**: [链接地址]
- **检测统计**: [统计信息]

---

*报告生成完毕 - [总体评估]*

---

# 📋 数据获取命令指南

## 🖥️ 系统配置信息获取

### 基础信息
```bash
# 操作系统信息
cat /etc/os-release
lsb_release -a

# 内核版本
uname -r
uname -a

# 系统架构
uname -m
arch

# 虚拟化信息
systemd-detect-virt
dmesg | grep -i hypervisor

# 时区和时间同步
timedatectl
timedatectl status

# 系统运行时间
uptime
uptime -p
```

### 硬件规格
```bash
# CPU信息
lscpu
cat /proc/cpuinfo
nproc

# 内存信息
free -h
cat /proc/meminfo
lsmem

# 存储信息
df -h
lsblk
fdisk -l

# 硬件详细信息
lshw -short
dmidecode -t system
```

## 🌐 网络配置信息获取

### IP地址和网络接口
```bash
# IP地址信息
ip addr show
ip route show
hostname

# 网络接口状态
ip link show
ifconfig -a

# DNS配置
cat /etc/resolv.conf
systemd-resolve --status
```

### Docker网络
```bash
# Docker网络列表
docker network ls
docker network inspect bridge

# 容器网络信息
docker ps --format "table {{.Names}}\t{{.Ports}}"
```

### 网络质量检测
```bash
# IP质量检测脚本
bash <(curl -sL Check.Place) -I

# 流媒体解锁检测
bash <(curl -L -s check.unlock.media)

# 三网回程路由检测
curl https://raw.githubusercontent.com/oneclickvirt/backtrace/main/backtrace.sh -sSf | bash

# 网络速度测试
curl -sL yabs.sh | bash -s -- -i
```

## 🚀 服务部署信息获取

### 容器服务
```bash
# Docker容器列表
docker ps -a
docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"

# 容器详细信息
docker inspect [容器名]
docker stats --no-stream
```

### 系统服务
```bash
# 运行中的服务
systemctl list-units --type=service --state=running
systemctl list-units --type=service --state=active

# 服务状态检查
systemctl status [服务名]
```

### 端口监听情况
```bash
# 监听端口详情
ss -tulnp | grep LISTEN
netstat -tulnp | grep LISTEN

# 特定端口检查
lsof -i :[端口号]
ss -tulnp | grep :[端口号]
```

## 📊 性能监控信息获取

### 系统负载和进程
```bash
# 系统负载
uptime
w
top -bn1 | head -20

# 进程信息
ps aux | head -20
ps -eo pid,ppid,cmd,%mem,%cpu --sort=-%mem | head

# 系统资源使用
htop
iotop
```

### 资源使用率
```bash
# CPU使用率
top -bn1 | grep "Cpu(s)"
vmstat 1 3

# 内存使用率
free -h
cat /proc/meminfo

# 磁盘使用率
df -h
du -sh /*

# 网络流量
iftop
nethogs
```

## 🚀 性能基准测试

### CPU性能测试
```bash
# 安装sysbench
apt update && apt install sysbench -y  # Ubuntu/Debian
yum install sysbench -y                # CentOS/RHEL

# CPU测试
sysbench cpu --cpu-max-prime=20000 --threads=1 run
sysbench cpu --cpu-max-prime=20000 --threads=$(nproc) run
```

### 内存性能测试
```bash
# 内存测试脚本
wget -qO- bench.sh | bash

# 或使用sysbench
sysbench memory --memory-block-size=1K --memory-scope=global --memory-total-size=10G --memory-oper=read run
sysbench memory --memory-block-size=1K --memory-scope=global --memory-total-size=10G --memory-oper=write run
```

### 磁盘I/O性能测试
```bash
# DD测试
dd if=/dev/zero of=test_file bs=4k count=25600 oflag=direct
dd if=test_file of=/dev/null bs=4k count=25600 iflag=direct
rm test_file

dd if=/dev/zero of=test_file bs=1M count=1024 oflag=direct
dd if=test_file of=/dev/null bs=1M count=1024 iflag=direct
rm test_file

# FIO测试
fio --name=test --ioengine=libaio --iodepth=64 --rw=randrw --bs=4k --direct=1 --size=1G --numjobs=1 --runtime=60 --group_reporting
```

### 网络速度测试
```bash
# 综合测试脚本
curl -sL yabs.sh | bash

# Speedtest
curl -s https://raw.githubusercontent.com/sivel/speedtest-cli/master/speedtest.py | python3

# iperf3测试
iperf3 -c [测试服务器]
```

## 📧 邮件服务测试

### 邮件端口连通性
```bash
# 邮件端口检测脚本
bash <(curl -sL https://raw.githubusercontent.com/oneclickvirt/portchecker/main/portchecker.sh)

# 手动端口检测
telnet smtp.gmail.com 587
telnet smtp.outlook.com 587
nc -zv smtp.qq.com 587
```

## 🔒 安全状态检查

### 端口扫描
```bash
# 本地端口扫描
nmap -sT -O localhost
ss -tulnp

# 防火墙状态
ufw status verbose
iptables -L -n
firewall-cmd --list-all
```

### 系统安全检查
```bash
# 登录用户
who
w
last | head -10

# 系统日志
journalctl -n 50
tail -f /var/log/auth.log
```

## 🛠️ 一键获取脚本示例

### 系统信息收集脚本
```bash
#!/bin/bash
echo "=== 系统基础信息 ==="
hostnamectl
echo ""
echo "=== CPU信息 ==="
lscpu | grep -E "Model name|CPU\(s\)|Thread|Core"
echo ""
echo "=== 内存信息 ==="
free -h
echo ""
echo "=== 磁盘信息 ==="
df -h
echo ""
echo "=== 网络信息 ==="
ip addr show | grep -E "inet |inet6 "
echo ""
echo "=== Docker容器 ==="
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo ""
echo "=== 监听端口 ==="
ss -tulnp | grep LISTEN
```

### 性能测试脚本
```bash
#!/bin/bash
echo "开始性能测试..."
echo "=== CPU测试 ==="
sysbench cpu --cpu-max-prime=20000 --threads=1 run | grep "events per second"
echo ""
echo "=== 磁盘测试 ==="
dd if=/dev/zero of=test_file bs=1M count=1024 oflag=direct 2>&1 | grep copied
rm -f test_file
echo ""
echo "=== 网络测试 ==="
curl -s https://raw.githubusercontent.com/sivel/speedtest-cli/master/speedtest.py | python3
```

---

## 📝 使用说明

1. **按需执行**: 根据模板需要的信息执行对应命令
2. **权限要求**: 某些命令需要root权限，使用sudo执行
3. **软件依赖**: 部分命令需要安装额外软件包
4. **安全注意**: 网络测试脚本请从可信源下载
5. **数据整理**: 将命令输出整理后填入模板对应位置

---

*命令指南完毕 - 请根据实际需求选择执行*
