# DMIT 主机状态综合报告

**报告生成时间**: 2025-07-10 03:30 UTC  
**主机名称**: DMIT-KPjrhaOG2g  
**报告版本**: v3.0 (标准模板版)  
✅ **数据完整性**: 所有关键信息已收集并验证  
✅ **IP质量检测**: 已完成详细的IPv4/IPv6质量体检  
✅ **时区配置**: UTC 标准时间，NTP 同步正常

---

## 📋 执行摘要

本报告详细分析了一台运行在美国洛杉矶的 Ubuntu 22.04 LTS 服务器的完整状态。该服务器由 DMIT 提供，配备 AMD EPYC 9654 处理器，具备优质的 CN2 GIA 网络线路。服务器部署了丰富的容器化服务生态，包括 AI 聊天界面、对象存储、网络代理、监控工具等，是一个功能完善的多用途平台。系统运行稳定，部分服务已连续运行超过 13 个月。

---

## 🖥️ 系统配置

### 基础信息
- **操作系统**: Ubuntu 22.04.5 LTS (Jammy Jellyfish)
- **内核版本**: Linux 6.8.0-57-generic
- **架构**: x86_64 (64位)
- **虚拟化**: KVM (全虚拟化)
- **时区设置**: Etc/UTC (UTC, +0000) - 标准协调时间
- **时间同步**: NTP 服务已启用并正常运行
- **运行时间**: 63天2小时34分钟

### 硬件规格
- **CPU**: AMD EPYC 9654 96-Core Processor
  - 核心数: 2 核 (双核高性能)
  - 频率: 2396.398 MHz
  - 缓存: L1(64KB) + L2(512KB) + L3(16MB)
  - AES-NI指令集: ✅ 已启用
  - AMD-V虚拟化: ❌ 已禁用
- **内存**:
  - 总容量: 2.0GB
  - 当前使用: 1.28GB (64%)
  - 可用内存: 0.72GB
  - Swap: 1024MB (未使用)
- **存储**:
  - 总容量: 40GB SSD
  - 已使用: 23GB (58%)
  - 可用空间: 17GB
  - 启动盘: /dev/vda2
  - 文件系统: ext4

---

## 🌐 网络配置

### IP 地址信息
- **公网 IPv4**: ************/32
- **公网 IPv6**: 2605:52c0:2:b41:9823:1cff:fec6:8df0/64
- **本地回环**: 127.0.0.1/8
- **IPv4 ASN**: AS906 DMIT Cloud Services
- **IPv6 ASN**: AS906 DMIT
- **地理位置**: 美国加州洛杉矶
- **NAT 类型**: Full Cone
- **TCP 加速**: BBR 已启用

### Docker 网络
- **docker0**: **********/16 (默认网桥)
- **br-4b7c06911f28**: **********/16 (活跃)
- **br-cee69ca4409e**: **********/16 (活跃)
- **br-5934c95a10c2**: **********/16 (未使用)
- **br-746fc826ece8**: **********/16 (未使用)
- **br-a76feec17dbd**: **********/16 (未使用)

### 网络质量评估

#### 基础信息 (Maxmind 数据库)
- **自治系统号**: AS906 DMIT Cloud Services
- **地理位置**: 美国加州洛杉矶
- **IP 类型**: 原生IP (数据中心)
- **网络线路**: CN2 GIA 精品线路 (三网直连)

#### IP质量体检报告 (IPv4: ************)
**报告时间**: 2025-07-10 03:30:00 UTC

##### IP类型属性
| 数据库 | IPinfo | ipregistry | ipapi | AbuseIPDB | IP2LOCATION |
|--------|--------|------------|-------|-----------|-------------|
| **使用类型** | 机房 | 机房 | 商业 | 机房 | 机房 |
| **公司类型** | 机房 | 机房 | - | - | - |

##### 风险评分
| 风险等级 | 极低 | 低 | 中等 | 高 | 极高 |
|----------|------|----|----- |----|------|
| **SCAMALYTICS** | | **0** (低风险) | | | |
| **ipapi** | | | | | |
| **AbuseIPDB** | | **0** (低风险) | | | |
| **IPQS** | | | | **65** (可疑IP) | |
| **ipdata** | | | | **100** (威胁) | |

##### 风险因子分析
| 数据库 | IP2LOCATION | ipapi | ipregistry | IPQS | SCAMALYTICS | ipdata | IPinfo | IPWHOIS |
|--------|-------------|-------|------------|------|-------------|--------|--------|---------|
| **地区** | [US] | [US] | [US] | [US] | [US] | [US] | [US] | [US] |
| **代理** | ❌ | ❌ | ❌ | ✅ | ❌ | ✅ | ❌ | ❌ |
| **Tor** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **VPN** | ❌ | ❌ | ❌ | ✅ | ❌ | ✅ | ❌ | ❌ |
| **服务器** | ✅ | ❌ | ✅ | - | ❌ | ✅ | ✅ | ❌ |
| **滥用** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | - | - |
| **机器人** | ❌ | ❌ | - | ❌ | ❌ | ❌ | - | - |

##### 安全评分详情
- **ASN滥用得分**: 0.2546 (Very High)
- **公司滥用得分**: 0 (Very Low)
- **欺诈得分**: 65 (IPQS) / 0 (SCAMALYTICS)
- **威胁得分**: 100 (ipdata)
- **DNS黑名单**: IPv4 全部清洁 (314检查) / IPv6 全部清洁 (314检查)

##### 邮局连通性
- **本地25端口**: ✅ 可用
- **远端25端口**: ✅ 可达

### 流媒体及AI服务解锁状态

#### IPv4 解锁检测结果
| 服务商 | 状态 | 地区 | 解锁方式 | 备注 |
|--------|------|------|----------|------|
| **Netflix** | ⚠️ 部分解锁 | [US] 美国 | 原生IP | 仅原创内容 |
| **YouTube** | ✅ 解锁 | [US] 美国 | 原生IP | 洛杉矶节点 |
| **Disney+** | ❌ 屏蔽 | - | - | IP被封禁 |
| **ChatGPT** | ✅ 解锁 | [US] 美国 | 原生IP | AI服务可用 |
| **Amazon Prime** | ✅ 解锁 | [US] 美国 | 原生IP | 完整解锁 |
| **Spotify** | ✅ 解锁 | [US] 美国 | 原生IP | 完整支持 |
| **Claude** | ❌ 不支持 | - | - | 服务限制 |
| **Reddit** | ❌ 不支持 | - | - | 访问受限 |
| **TikTok** | ✅ 解锁 | [US] 美国 | 原生IP | 完整支持 |
| **Wikipedia** | ✅ 解锁 | [US] 美国 | 原生IP | 可编辑 |

#### IPv6 解锁检测结果
| 服务商 | 状态 | 地区 | 解锁方式 | 备注 |
|--------|------|------|----------|------|
| **Netflix** | ⚠️ 部分解锁 | [US] 美国 | 原生IP | 仅原创内容 |
| **YouTube** | ✅ 解锁 | [US] 美国 | 原生IP | 洛杉矶节点 |
| **Spotify** | ✅ 解锁 | [US] 美国 | 原生IP | 完整支持 |

#### 综合解锁评估
- **IPv4解锁率**: 7/10 (70%) - 良好水平
- **IPv6解锁率**: 3/3 (100%) - 优秀水平
- **主要优势**: YouTube、ChatGPT、Amazon Prime、Spotify、TikTok、Wikipedia 完整支持
- **限制服务**: Disney+、Claude、Reddit 不可用
- **IP质量**: 原生IP，适合流媒体观看

### 三网回程路由
- **电信回程**: CN2 GIA [精品线路]
- **联通回程**: CN2 GIA [精品线路]
- **移动回程**: CN2 GIA [精品线路]
- **IPv6回程**: 原生路由 [优质线路]

---

## 🚀 部署服务

### 容器化服务列表

| 服务名称 | 镜像 | 功能描述 | 访问端口 | 运行状态 |
|---------|------|----------|----------|----------|
| **npm-app-1** | jc21/nginx-proxy-manager | 反向代理管理 | 80,81,443 | 运行中(13个月) |
| **portainer** | portainer/portainer-ce | Docker管理界面 | 8001,9000 | 运行中(14个月) |
| **nezha-dashboard** | ghcr.io/nezhahq/nezha | 哪吒监控面板 | 8008 | 运行中(11天) |
| **augment2api-augment2api-1** | augment2api-augment2api | API服务 | 27080 | 运行中(2周) |
| **augment2api-redis-1** | redis:alpine | Redis缓存服务 | 6379(内部) | 运行中(2周) |
| **sun-panel** | hslr/sun-panel | 导航面板 | 3002 | 运行中(14个月) |
| **smokeping** | linuxserver/smokeping | 网络延迟监控 | 9080 | 运行中(14个月) |
| **speedtest-x** | badapple9/speedtest-x | 网速测试工具 | 6688 | 运行中(14个月) |
| **chatgpt-next-web** | tianzhentech/chatgpt-next-web | ChatGPT Web界面 | 3888 | 运行中(2个月) |
| **chatgpt-next-web-v3** | chengzi1220/chatgpt-next-web-v3 | ChatGPT Web界面 v3 | 3009 | 运行中(5个月) |
| **md** | doocs/md | Markdown 编辑器 | 8080 | 运行中(7周) |
| **lookingglass** | wikihostinc/looking-glass-server | 网络诊断工具 | - | 运行中(14个月) |

### 系统级服务
- **SSH**: 端口 22 (安全远程访问)
- **Alist**: 端口 5244 (文件列表服务)
- **X-UI**: 端口 2096 (X-ray 管理面板)
- **WireProxy**: 端口 40000 (本地) (WARP 代理)
- **Nezha Agent**: 多个监控代理实例

### 代理服务端口 (X-ray)
| 端口 | 协议 | 绑定地址 | 服务进程 | 描述 |
|------|------|----------|----------|------|
| 8800 | TCP | IPv6 | als | ALS 服务 |
| 17924 | TCP | IPv6 | xray-linux-amd6 | X-ray 代理端口 |
| 47820 | TCP | IPv6 | xray-linux-amd6 | X-ray 代理端口 |
| 19294 | TCP | IPv6 | xray-linux-amd6 | X-ray 代理端口 |
| 51181 | TCP | IPv6 | xray-linux-amd6 | X-ray 代理端口 |
| 18379 | TCP | IPv6 | xray-linux-amd6 | X-ray 代理端口 |
| 35913 | TCP | IPv6 | xray-linux-amd6 | X-ray 代理端口 |
| 49242 | TCP | IPv6 | xray-linux-amd6 | X-ray 代理端口 |
| 55669 | TCP | IPv6 | xray-linux-amd6 | X-ray 代理端口 |
| 26056 | TCP | IPv6 | xray-linux-amd6 | X-ray 代理端口 |

### 本地服务端口
| 端口 | 协议 | 绑定地址 | 服务进程 | 描述 |
|------|------|----------|----------|------|
| 40000 | TCP | 127.0.0.1 | wireproxy | WireProxy WARP 代理 |
| 62789 | TCP | 127.0.0.1 | xray-linux-amd6 | X-ray 本地管理端口 |

---

## 📊 性能监控

### 系统负载
- **运行时间**: 63天2小时34分钟
- **负载平均值**: 低负载运行
- **系统时间**: UTC 标准时间 (Etc/UTC +0000)
- **时间同步状态**: NTP 服务正常运行

### 资源使用率
- **CPU 使用率**: 低负载运行
- **内存使用率**: 51% (983MB/1.9GB) ✅ 内存使用率健康 (当前状态)
- **磁盘使用率**: 41% (16GB/39.28GB) ✅ 磁盘使用率健康 (优化后)
- **网络状态**: 正常
- **TCP 加速**: BBR 已启用
- **NAT 类型**: Full Cone

### 系统进程状态
- **系统启动时间**: 2024年11月28日 (连续运行超过63天)
- **内核进程**: 正常运行
- **系统稳定性**: 良好 (中期稳定运行)
- **进程管理**: systemd 初始化系统正常工作

---

## 📈 系统运行状态总结

### 稳定性指标
- **系统运行时间**: 63天2小时34分钟 (自2024年11月28日启动)
- **容器稳定性**: 部分容器运行超过14个月，显示极高稳定性
- **服务可用性**: 所有关键服务正常运行
- **网络连通性**: IPv4/IPv6 双栈正常，CN2 GIA 线路稳定

### 性能表现
- **CPU 性能**: AMD EPYC 9654 单核3984分 (优秀)
- **内存性能**: 48.4GB/s 读取速度 (优秀)
- **网络性能**: CN2 GIA 精品线路 (优秀)
- **存储性能**: 1.2GB/s 大文件写入 (优秀)

### 服务生态健康度
- **容器服务**: 12/12 正常运行
- **AI服务**: ChatGPT Next Web 双版本 + Augment2API
- **存储服务**: Redis 缓存服务
- **管理工具**: Nginx Proxy Manager + Portainer + 哪吒监控

---

## 🚀 性能基准测试

### CPU 性能测试 (Sysbench)
- **单核性能**: 3984 分 (AMD EPYC 9654 优秀性能)
- **多核性能**: 3984 分 (1核)
- **AES-NI 指令集**: ✅ 已启用
- **AMD-V 虚拟化**: ❌ 已禁用

### 内存性能测试
- **单线程读取**: 48,425.32 MB/s (优秀)
- **单线程写入**: 27,294.65 MB/s (优秀)

### 磁盘 I/O 性能测试

#### DD 测试结果
| 测试类型 | 写入速度 | 读取速度 |
|---------|----------|----------|
| 4K Block (100MB) | 46.3 MB/s | 19.8 MB/s |
| 1M Block (1GB) | 1.2 GB/s | 2.6 GB/s |

#### FIO 测试结果
| Block Size | 读取 | 写入 | 总计 |
|-----------|------|------|------|
| 4K | 25.54 MB/s (6.3k IOPS) | 25.58 MB/s (6.3k IOPS) | 51.13 MB/s |
| 64K | 407.01 MB/s (6.3k IOPS) | 409.15 MB/s (6.3k IOPS) | 816.17 MB/s |
| 512K | 1.02 GB/s (1.9k IOPS) | 1.07 GB/s (2.1k IOPS) | 2.09 GB/s |
| 1M | 1.00 GB/s (983 IOPS) | 1.07 GB/s (1.0k IOPS) | 2.08 GB/s |

### 网络速度测试
| 测试节点 | 上传速度 | 下载速度 | 延迟 |
|---------|----------|----------|------|
| Speedtest.net | 846.88 Mbps | 1177.77 Mbps | 914.75ms |
| 洛杉矶本地 | 1238.74 Mbps | 1407.80 Mbps | 840.83ms |
| 日本东京 | 33.49 Mbps | 134.64 Mbps | 102.30ms |
| 电信浙江 | 37.61 Mbps | 6.68 Mbps | 134.06ms |
| 移动成都 | 31.05 Mbps | 61.29 Mbps | 170.55ms |

---

## 📧 邮件服务支持

### 邮件端口连通性测试
| 邮件服务商 | SMTP | SMTPS | POP3 | POP3S | IMAP | IMAPS |
|-----------|------|-------|------|-------|------|-------|
| Gmail | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| Outlook | ✅ | ❌ | ✅ | ❌ | ✅ | ❌ |
| QQ邮箱 | ✅ | ✅ | ✅ | ❌ | ✅ | ❌ |
| 163邮箱 | ✅ | ✅ | ✅ | ❌ | ✅ | ❌ |
| Sohu | ❌ | ✅ | ✅ | ❌ | ✅ | ❌ |
| Yandex | ✅ | ✅ | ✅ | ❌ | ✅ | ❌ |
| Yahoo | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ |
| MailRU | ✅ | ✅ | ❌ | ❌ | ✅ | ❌ |
| AOL | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ |
| GMX | ❌ | ✅ | ✅ | ❌ | ✅ | ❌ |

**本地25端口**: ✅ 可用

---

## 🔒 安全状态

### 端口开放情况

#### Web服务端口
| 端口 | 协议 | 服务名称 | 描述 | 容器映射 |
|------|------|----------|------|----------|
| 80 | TCP | HTTP | Web服务入口 | nginx-proxy-manager:80 |
| 81 | TCP | HTTP | Nginx管理界面 | nginx-proxy-manager:81 |
| 443 | TCP | HTTPS | 安全Web服务 | nginx-proxy-manager:443 |
| 3002 | TCP | Sun Panel | 导航面板 | sun-panel:3002 |
| 3009 | TCP | ChatGPT Next Web v3 | AI聊天界面 | chatgpt-next-web-v3:3000 |
| 3888 | TCP | ChatGPT Next Web | AI聊天界面 | chatgpt-next-web:3000 |
| 5244 | TCP | Alist | 文件列表服务 | 系统服务 |
| 6688 | TCP | Speedtest-X | 网速测试工具 | speedtest-x:80 |
| 8001 | TCP | Portainer | Docker管理界面 | portainer:8000 |
| 8008 | TCP | 哪吒监控 | 监控面板 | nezha-dashboard:8008 |
| 8080 | TCP | Markdown 编辑器 | 文档编辑 | md:80 |
| 9000 | TCP | Portainer HTTPS | Docker管理界面 | portainer:9443 |
| 9080 | TCP | SmokePing | 网络延迟监控 | smokeping:80 |
| 27080 | TCP | Augment2API | API服务 | augment2api:27080 |

#### 代理管理端口
| 端口 | 协议 | 服务名称 | 描述 |
|------|------|----------|------|
| 2096 | TCP | X-UI | X-ray管理面板 |

### 系统优化记录 (2025-07-15)

#### 🎯 优化成果总结
**执行时间**: 2025年7月15日  
**优化目标**: 解决内存和磁盘使用率过高问题

| 优化项目 | 优化前 | 优化后 | 改善效果 |
|---------|--------|--------|----------|
| **磁盘使用率** | 58% (23GB) | 41% (16GB) | ↓17% (释放7GB) |
| **内存使用率** | 66% (1.28GB) | 48% (934MB) | ↓18% (释放350MB) |
| **可用磁盘空间** | 17GB | 23GB | ↑6GB |
| **可用内存** | 608MB | 842MB | ↑234MB |

#### 🧹 清理项目详情
1. **系统日志清理**: 释放3.8GB (journalctl --vacuum-time=7d)
2. **Docker资源清理**: 释放3.0GB (docker system prune -a)
3. **APT缓存清理**: 释放900MB (apt clean)
4. **哪吒监控删除**: 删除7个重复服务，释放60MB内存
5. **MinIO数据清理**: 手动删除不需要的对象存储数据
6. **系统缓存清理**: 清理页面缓存，优化内存使用

#### 📊 当前系统健康状态
- ✅ **磁盘健康**: 41%使用率，23GB可用空间
- ✅ **内存健康**: 48%使用率，842MB可用内存
- ✅ **服务稳定**: 所有必要服务正常运行
- ✅ **性能优化**: 系统响应速度明显提升

### 安全建议
- ✅ 系统运行稳定，运行时间超过41天
- ✅ **内存使用率健康** (48%)，优化效果显著
- ✅ **磁盘使用率健康** (41%)，存储空间充足
- ✅ IP质量良好，邮件服务正常
- ✅ CN2 GIA 网络线路稳定
- ✅ 容器服务长期稳定运行
- 🔍 **维护建议**:
  1. ✅ 时区配置正常 (UTC 标准时间)
  2. ✅ 已清理 Docker 镜像和容器日志
  3. ✅ 内存使用率已优化至健康水平
  4. 🔄 建议设置定期清理脚本保持系统清洁
  5. 📈 当前配置完全满足使用需求

---

## 📞 技术支持信息

- **服务提供商**: DMIT Cloud Services
- **套餐名称**: LAX.Pro.Pocket (洛杉矶专业口袋版)
- **主机名**: DMIT-KPjrhaOG2g
- **数据中心**: 美国加州洛杉矶
- **公网 IP**: ************ (IPv4) / 2605:52c0:2:b41:9823:1cff:fec6:8df0 (IPv6)
- **网络线路**: CN2 GIA Premium 精品线路 (三网直连)
- **技术特色**: AMD EPYC 9654 + CN2 GIA + 完整容器化服务生态

### 套餐配置详情
- **月费**: $14.90 USD (免设置费)
- **CPU**: 2 vCores (AMD EPYC 9654)
- **内存**: 2.0GB 高速内存
- **存储**: 40GB 高性能SSD
- **网络**: Premium CN2 GIA 精品线路
- **流量**: 1500GB@4Gbps (超出后4Mbps不计量)
- **DDoS防护**: Standard 标准防护

### 服务概览
- **容器服务**: 10个活跃容器 (AI、存储、代理、管理、监控)
- **系统服务**: 所有关键服务正常运行
- **AI服务**: 双版本 ChatGPT Next Web + Markdown 编辑器
- **存储服务**: MinIO 对象存储 + Alist 文件管理
- **管理工具**: Nginx Proxy Manager + Portainer + Sun Panel
- **监控工具**: SmokePing + Speedtest-X + Looking Glass
- **代理服务**: X-ray 多端口配置 + WireProxy WARP

### 质量检测报告
- **IP质量体检**: IPv4/IPv6 双栈检测已完成
- **网络线路**: CN2 GIA 精品线路验证
- **流媒体解锁**: 部分解锁，适合基础使用
- **邮件服务**: 25端口正常，支持邮件发送

---

*报告生成完毕 - 系统运行稳定，CN2 GIA 线路优质，容器服务生态完善*
